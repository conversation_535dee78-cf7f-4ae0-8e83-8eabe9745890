# browser_automation.py - Playwright浏览器自动化
"""
使用Playwright自动化浏览器访问微信公众号文章
配置代理通过mitmproxy进行抓包
"""

import asyncio
import time
import random
import html
from playwright.async_api import async_playwright
from config import PROXY_URL, BROWSER_CONFIG, EXTRACT_CONFIG


class WechatBrowserAutomation:
    """微信浏览器自动化类"""
    
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        
    async def start_browser(self):
        """启动浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器，配置代理
            self.browser = await self.playwright.chromium.launch(
                headless=BROWSER_CONFIG["headless"],
                slow_mo=BROWSER_CONFIG["slow_mo"]
            )
            
            # 创建浏览器上下文，配置代理
            self.context = await self.browser.new_context(
                proxy={"server": PROXY_URL},
                viewport=BROWSER_CONFIG["viewport"],
                user_agent=BROWSER_CONFIG["user_agent"],
                ignore_https_errors=True  # 忽略HTTPS证书错误
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置超时时间
            self.page.set_default_timeout(BROWSER_CONFIG["timeout"])
            
            print(f"✅ 浏览器启动成功，代理: {PROXY_URL}")
            return True
            
        except Exception as e:
            print(f"❌ 启动浏览器失败: {e}")
            return False
    
    async def visit_article(self, url, wait_time=None):
        """访问微信文章"""
        if not self.page:
            print("❌ 浏览器未启动")
            return False

        try:
            # 解码HTML编码的URL
            decoded_url = html.unescape(url)
            print(f"🌐 正在访问: {decoded_url}")

            # 访问页面
            response = await self.page.goto(decoded_url, wait_until="networkidle")
            
            if response and response.status == 200:
                print(f"✅ 页面加载成功: {response.status}")
                
                # 等待页面完全加载
                await asyncio.sleep(EXTRACT_CONFIG["page_load_wait"])
                
                # 模拟用户行为：滚动页面
                await self.simulate_user_behavior()
                
                # 额外等待时间
                if wait_time:
                    print(f"⏳ 等待 {wait_time} 秒...")
                    await asyncio.sleep(wait_time)
                
                return True
            else:
                print(f"❌ 页面加载失败: {response.status if response else 'No response'}")
                return False
                
        except Exception as e:
            print(f"❌ 访问文章失败: {e}")
            return False
    
    async def simulate_user_behavior(self):
        """模拟用户行为"""
        try:
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 模拟滚动
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight / 3)")
            await asyncio.sleep(EXTRACT_CONFIG["scroll_wait"])
            
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight / 2)")
            await asyncio.sleep(EXTRACT_CONFIG["scroll_wait"])
            
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(EXTRACT_CONFIG["scroll_wait"])
            
            # 回到顶部
            await self.page.evaluate("window.scrollTo(0, 0)")
            await asyncio.sleep(1)
            
            print("✅ 用户行为模拟完成")
            
        except Exception as e:
            print(f"⚠️ 模拟用户行为时出错: {e}")
    
    async def batch_visit_articles(self, urls, wait_time_per_article=10):
        """批量访问文章"""
        results = []
        
        for i, url in enumerate(urls, 1):
            print(f"\n📖 处理第 {i}/{len(urls)} 篇文章")
            
            success = await self.visit_article(url, wait_time_per_article)
            results.append({
                'url': url,
                'success': success,
                'timestamp': time.time()
            })
            
            # 随机等待，避免被检测
            if i < len(urls):
                wait_time = random.uniform(3, 8)
                print(f"⏳ 随机等待 {wait_time:.1f} 秒...")
                await asyncio.sleep(wait_time)
        
        return results
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            print("✅ 浏览器已关闭")
        except Exception as e:
            print(f"⚠️ 关闭浏览器时出错: {e}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_browser()


# 同步包装器
class WechatBrowserSync:
    """同步版本的浏览器自动化"""
    
    def __init__(self):
        self.automation = WechatBrowserAutomation()
    
    def visit_articles(self, urls, wait_time_per_article=10):
        """同步访问文章"""
        return asyncio.run(self._visit_articles_async(urls, wait_time_per_article))
    
    async def _visit_articles_async(self, urls, wait_time_per_article):
        """异步访问文章的内部实现"""
        async with self.automation:
            return await self.automation.batch_visit_articles(urls, wait_time_per_article)
