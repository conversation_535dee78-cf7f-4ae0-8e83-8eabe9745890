#!/usr/bin/env python3
# test_url_decode.py - 测试URL解码功能

import html

def test_url_decode():
    """测试URL解码功能"""
    
    # 测试URL（包含HTML编码）
    encoded_url = "http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&amp;amp;mid=2649923002&amp;amp;idx=1&amp;amp;sn=5f5b5ea491b3afd997354d8eab8deab0&amp;amp;chksm=bf8e2ea346f0989a2800145975b0f4685e5899c1e669ad4cbb1ca1ea9a721c15c3603f7c9483&amp;amp;scene=27#wechat_redirect"
    
    print("🔍 URL解码测试")
    print("=" * 60)
    print(f"原始URL: {encoded_url}")
    print()
    
    # 解码URL
    decoded_url = html.unescape(encoded_url)
    print(f"解码后URL: {decoded_url}")
    print()
    
    # 检查解码效果
    if "&amp;amp;" in encoded_url and "&" in decoded_url and "&amp;amp;" not in decoded_url:
        print("✅ URL解码成功！")
        print("✅ HTML编码字符已正确转换")
    else:
        print("❌ URL解码可能有问题")
    
    return decoded_url

if __name__ == "__main__":
    test_url_decode()
