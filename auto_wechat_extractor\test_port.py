# test_port.py - 端口测试脚本
"""
测试端口检查和mitmproxy启动功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import ProcessManager
import time


def test_port_detection():
    """测试端口检测功能"""
    print("🔍 测试端口检测功能")
    
    pm = ProcessManager()
    
    # 测试端口检查
    test_ports = [8080, 8081, 8082, 8083]
    
    for port in test_ports:
        available = pm.is_port_available(port)
        status = "✅ 可用" if available else "❌ 被占用"
        print(f"端口 {port}: {status}")
    
    # 查找可用端口
    available_port = pm.find_available_port(8081)
    if available_port:
        print(f"🎯 找到可用端口: {available_port}")
    else:
        print("❌ 未找到可用端口")
    
    return available_port


def test_mitmproxy_start():
    """测试mitmproxy启动"""
    print("\n🚀 测试mitmproxy启动")
    
    pm = ProcessManager()
    
    # 获取脚本路径
    script_path = os.path.join(os.path.dirname(__file__), 'proxy_interceptor.py')
    
    if not os.path.exists(script_path):
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    print(f"📄 使用脚本: {script_path}")
    
    # 启动mitmproxy
    success, port = pm.start_mitmproxy(script_path)
    
    if success:
        print(f"✅ mitmproxy启动成功，端口: {port}")
        
        # 等待几秒钟
        print("⏳ 等待5秒钟...")
        time.sleep(5)
        
        # 停止mitmproxy
        print("🛑 停止mitmproxy...")
        pm.stop_mitmproxy()
        
        return True
    else:
        print("❌ mitmproxy启动失败")
        return False


def main():
    """主测试函数"""
    print("🧪 端口和mitmproxy测试工具")
    print("=" * 40)
    
    try:
        # 测试端口检测
        available_port = test_port_detection()
        
        if available_port:
            # 测试mitmproxy启动
            success = test_mitmproxy_start()
            
            if success:
                print("\n🎉 所有测试通过！")
                print("✅ 端口检测正常")
                print("✅ mitmproxy启动正常")
            else:
                print("\n❌ mitmproxy启动测试失败")
        else:
            print("\n❌ 端口检测测试失败")
    
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
