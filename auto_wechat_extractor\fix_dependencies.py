# fix_dependencies.py - 修复依赖问题
"""
修复bcrypt版本兼容性和其他依赖问题
"""

import subprocess
import sys
import os


def fix_bcrypt():
    """修复bcrypt版本问题"""
    print("🔧 修复bcrypt版本问题...")
    
    try:
        # 卸载旧版本
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "bcrypt", "-y"], 
                      capture_output=True, text=True)
        
        # 安装兼容版本
        result = subprocess.run([sys.executable, "-m", "pip", "install", "bcrypt==4.0.1"], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ bcrypt修复成功")
            return True
        else:
            print(f"❌ bcrypt修复失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 修复bcrypt时出错: {e}")
        return False


def fix_mitmproxy():
    """修复mitmproxy"""
    print("🔧 修复mitmproxy...")
    
    try:
        # 重新安装mitmproxy
        result = subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "mitmproxy"], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ mitmproxy修复成功")
            return True
        else:
            print(f"❌ mitmproxy修复失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 修复mitmproxy时出错: {e}")
        return False


def kill_port_processes():
    """杀死占用端口的进程"""
    print("🔧 清理端口占用...")
    
    ports_to_check = [8080, 8081, 8082, 8083]
    
    for port in ports_to_check:
        try:
            # 查找占用端口的进程
            result = subprocess.run(f"netstat -ano | findstr :{port}", 
                                  shell=True, capture_output=True, text=True)
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            try:
                                # 杀死进程
                                kill_result = subprocess.run(f"taskkill /F /PID {pid}", 
                                                           shell=True, capture_output=True, text=True)
                                if kill_result.returncode == 0:
                                    print(f"✅ 已清理端口 {port} (PID: {pid})")
                                else:
                                    print(f"⚠️ 无法清理端口 {port} (PID: {pid})")
                            except:
                                pass
            else:
                print(f"✅ 端口 {port} 未被占用")
                
        except Exception as e:
            print(f"⚠️ 检查端口 {port} 时出错: {e}")


def install_all_dependencies():
    """安装所有依赖"""
    print("📦 安装所有依赖...")
    
    dependencies = [
        "mitmproxy",
        "playwright",
        "requests",
        "pandas",
        "beautifulsoup4",
        "lxml",
        "python-dateutil",
        "urllib3",
        "certifi"
    ]
    
    for dep in dependencies:
        try:
            print(f"📦 安装 {dep}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                                   capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {dep} 安装成功")
            else:
                print(f"❌ {dep} 安装失败: {result.stderr}")
                
        except Exception as e:
            print(f"❌ 安装 {dep} 时出错: {e}")
    
    # 安装playwright浏览器
    try:
        print("🌐 安装playwright浏览器...")
        result = subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ playwright浏览器安装成功")
        else:
            print(f"❌ playwright浏览器安装失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 安装playwright浏览器时出错: {e}")


def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    test_modules = [
        ("mitmproxy", "mitmproxy"),
        ("playwright", "playwright"),
        ("requests", "requests"),
        ("pandas", "pandas"),
        ("bs4", "beautifulsoup4")
    ]
    
    all_ok = True
    
    for module_name, package_name in test_modules:
        try:
            __import__(module_name)
            print(f"✅ {package_name}: 导入成功")
        except ImportError as e:
            print(f"❌ {package_name}: 导入失败 - {e}")
            all_ok = False
    
    return all_ok


def main():
    """主修复函数"""
    print("🛠️ 自动化微信抓取工具 - 依赖修复工具")
    print("=" * 50)
    
    print("选择修复选项:")
    print("1. 修复bcrypt版本问题")
    print("2. 清理端口占用")
    print("3. 重新安装所有依赖")
    print("4. 测试导入")
    print("5. 全部修复")
    print("6. 退出")
    
    choice = input("\n请选择 (1-6): ").strip()
    
    if choice == '1':
        fix_bcrypt()
    elif choice == '2':
        kill_port_processes()
    elif choice == '3':
        install_all_dependencies()
    elif choice == '4':
        test_imports()
    elif choice == '5':
        print("🚀 执行全部修复...")
        kill_port_processes()
        fix_bcrypt()
        fix_mitmproxy()
        install_all_dependencies()
        
        print("\n🧪 测试修复结果...")
        if test_imports():
            print("\n🎉 所有修复完成！")
        else:
            print("\n⚠️ 部分修复可能未成功，请检查错误信息")
    elif choice == '6':
        print("👋 退出修复工具")
    else:
        print("❌ 无效选择")


if __name__ == '__main__':
    main()
