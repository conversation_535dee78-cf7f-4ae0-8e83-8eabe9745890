# quick_start.py - 快速启动脚本
"""
解决端口占用问题的快速启动脚本
自动检测可用端口并启动抓取工具
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auto_extractor import AutoWechatExtractor


def quick_demo():
    """快速演示"""
    print("🎯 自动化微信参数抓取工具 - 快速启动")
    print("=" * 50)
    print("✅ 已修复端口占用问题")
    print("✅ 自动检测可用端口")
    print("✅ 智能重试机制")
    print()
    
    # 示例链接（请替换为实际的微信文章链接）
    demo_urls = [
        "https://mp.weixin.qq.com/s/demo_link_1",
        "https://mp.weixin.qq.com/s/demo_link_2"
    ]
    
    print("📝 演示配置:")
    print("   - 自动检测可用端口（避免8080冲突）")
    print("   - 超时时间: 60秒")
    print("   - 每篇文章等待: 10秒")
    print()
    
    # 获取用户输入
    use_demo = input("是否使用演示链接？(y/N，建议输入实际链接): ").strip().lower()
    
    if use_demo not in ['y', 'yes']:
        print("\n请输入实际的微信文章链接（每行一个，输入空行结束）:")
        urls = []
        while True:
            url = input().strip()
            if not url:
                break
            urls.append(url)
        
        if not urls:
            print("❌ 未输入任何链接，使用演示链接")
            urls = demo_urls
    else:
        urls = demo_urls
        print("⚠️ 使用演示链接，实际抓取可能失败")
    
    print(f"\n📋 准备抓取 {len(urls)} 个链接")
    
    # 创建抓取器
    extractor = AutoWechatExtractor()
    
    try:
        # 执行抓取
        result = extractor.auto_extract(
            article_urls=urls,
            timeout=60,
            wait_per_article=10
        )
        
        if result:
            print("\n🎉 抓取成功！")
            print("📊 抓取结果:")
            print(f"   时间: {result.get('timestamp', 'N/A')}")
            print(f"   __biz: {result.get('biz', 'N/A')[:30]}...")
            print(f"   token: {result.get('appmsg_token', 'N/A')[:30]}...")
            
            # 保存兼容格式
            save_compatible = input("\n💾 是否保存为兼容格式？(Y/n): ").strip().lower()
            if save_compatible not in ['n', 'no']:
                if extractor.save_to_compatible_format("../wechat_keys.txt"):
                    print("✅ 已保存到 wechat_keys.txt，现有工具可直接使用")
        else:
            print("\n❌ 抓取失败")
            print("💡 可能的原因:")
            print("   1. 使用了演示链接（无效链接）")
            print("   2. 网络连接问题")
            print("   3. 微信文章链接无效")
            print("   4. 浏览器启动失败")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行过程出错: {e}")
        import traceback
        traceback.print_exc()


def check_environment():
    """检查环境"""
    print("🔍 检查运行环境...")
    
    # 检查依赖
    missing_deps = []
    
    try:
        import mitmproxy
        print("✅ mitmproxy: 已安装")
    except ImportError:
        missing_deps.append("mitmproxy")
        print("❌ mitmproxy: 未安装")
    
    try:
        import playwright
        print("✅ playwright: 已安装")
    except ImportError:
        missing_deps.append("playwright")
        print("❌ playwright: 未安装")
    
    try:
        import requests
        print("✅ requests: 已安装")
    except ImportError:
        missing_deps.append("requests")
        print("❌ requests: 未安装")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
        print("然后运行: playwright install chromium")
        return False
    
    print("✅ 环境检查通过")
    return True


def main():
    """主函数"""
    print("🚀 自动化微信参数抓取工具 - 快速启动")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        return
    
    print("\n选择操作:")
    print("1. 快速演示")
    print("2. 仅测试端口")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        quick_demo()
    elif choice == '2':
        from test_port import test_port_detection
        test_port_detection()
    elif choice == '3':
        print("👋 再见！")
    else:
        print("❌ 无效选择")


if __name__ == '__main__':
    main()
