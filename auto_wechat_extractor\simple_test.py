#!/usr/bin/env python3
# simple_test.py - 简单的Playwright测试

import asyncio
from playwright.async_api import async_playwright

async def simple_test():
    print("🧪 简单Playwright测试")
    print("=" * 40)
    
    try:
        async with async_playwright() as p:
            print("✅ Playwright启动成功")
            
            # 启动浏览器
            browser = await p.chromium.launch(headless=False)
            print("✅ 浏览器启动成功")
            
            # 创建页面
            page = await browser.new_page()
            print("✅ 页面创建成功")
            
            # 访问百度测试基本功能
            print("🌐 访问百度测试...")
            await page.goto("https://www.baidu.com")
            title = await page.title()
            print(f"✅ 百度页面标题: {title}")
            
            # 等待2秒
            await asyncio.sleep(2)
            
            # 尝试访问微信链接
            print("🌐 尝试访问微信链接...")
            wechat_url = "https://mp.weixin.qq.com"
            
            try:
                await page.goto(wechat_url, timeout=15000)
                title = await page.title()
                print(f"✅ 微信页面标题: {title}")
            except Exception as e:
                print(f"❌ 访问微信失败: {e}")
            
            await asyncio.sleep(3)
            await browser.close()
            print("✅ 测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_test())
