#!/usr/bin/env python3
# test_playwright_wechat.py - 测试Playwright访问微信链接

import asyncio
import html
from playwright.async_api import async_playwright

async def test_wechat_access():
    """测试Playwright访问微信链接"""
    
    # 测试URL（解码后的正确格式）
    test_url = "http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&mid=2649923002&idx=1&sn=5f5b5ea491b3afd997354d8eab8deab0&chksm=bf8e2ea346f0989a2800145975b0f4685e5899c1e669ad4cbb1ca1ea9a721c15c3603f7c9483&scene=27#wechat_redirect"
    
    print("🧪 Playwright微信链接访问测试")
    print("=" * 60)
    print(f"测试URL: {test_url}")
    print()
    
    async with async_playwright() as p:
        try:
            # 启动浏览器（不使用代理，先测试基本访问）
            print("🚀 启动浏览器...")
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器窗口
                slow_mo=1000,    # 慢速模式便于观察
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )
            
            # 创建页面
            context = await browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            page = await context.new_page()
            
            print("✅ 浏览器启动成功")
            
            # 尝试访问微信链接
            print("🌐 尝试访问微信链接...")
            try:
                response = await page.goto(test_url, wait_until="networkidle", timeout=30000)
                
                if response:
                    print(f"📡 响应状态: {response.status}")
                    print(f"📡 响应URL: {response.url}")
                    
                    if response.status == 200:
                        print("✅ 页面加载成功")
                        
                        # 获取页面标题
                        title = await page.title()
                        print(f"📄 页面标题: {title}")
                        
                        # 检查页面内容
                        content = await page.content()
                        if "微信公众平台" in content or "mp.weixin.qq.com" in content:
                            print("✅ 确认是微信页面")
                        else:
                            print("⚠️ 页面内容可能不是预期的微信页面")
                            
                        # 等待5秒让用户观察
                        print("⏳ 等待5秒供观察...")
                        await asyncio.sleep(5)
                        
                    else:
                        print(f"❌ 页面加载失败，状态码: {response.status}")
                else:
                    print("❌ 没有收到响应")
                    
            except Exception as e:
                print(f"❌ 访问页面时出错: {e}")
                print(f"错误类型: {type(e).__name__}")
                
            # 关闭浏览器
            await browser.close()
            print("✅ 浏览器已关闭")
            
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            print(f"错误类型: {type(e).__name__}")

async def test_with_proxy():
    """测试带代理的访问"""
    print("\n" + "=" * 60)
    print("🧪 测试带代理的访问")
    print("=" * 60)
    
    # 这里可以添加代理测试代码
    print("⚠️ 代理测试需要先启动mitmproxy")
    print("建议先测试基本访问是否正常")

if __name__ == "__main__":
    asyncio.run(test_wechat_access())
    # asyncio.run(test_with_proxy())
