# auto_extractor.py - 自动化微信参数抓取主程序
"""
自动化微信公众号参数抓取工具
结合mitmproxy + Playwright实现全自动抓包
"""

import os
import sys
import time
import asyncio
import threading
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from browser_automation import WechatBrowserAutomation, WechatBrowserSync
from utils import ProcessManager, DataParser, FileManager, validate_wechat_url, format_duration
from config import EXTRACT_CONFIG, OUTPUT_FILE


class AutoWechatExtractor:
    """自动化微信参数抓取器"""
    
    def __init__(self):
        self.process_manager = ProcessManager()
        self.data_parser = DataParser()
        self.file_manager = FileManager()
        self.extracted_data = None
        
    def auto_extract(self, article_urls, timeout=60, wait_per_article=10):
        """
        自动抓取微信参数
        
        Args:
            article_urls: 微信文章URL列表
            timeout: 总超时时间(秒)
            wait_per_article: 每篇文章等待时间(秒)
        
        Returns:
            dict: 抓取结果
        """
        print("🚀 启动自动化微信参数抓取器")
        print(f"📋 配置: {len(article_urls)}篇文章, 超时{timeout}秒, 每篇等待{wait_per_article}秒")
        
        start_time = time.time()
        
        try:
            # 1. 验证URL
            valid_urls = self._validate_urls(article_urls)
            if not valid_urls:
                return None
            
            # 2. 备份现有文件
            if os.path.exists(OUTPUT_FILE):
                self.file_manager.backup_file(OUTPUT_FILE)
            
            # 3. 启动mitmproxy
            if not self._start_proxy():
                return None
            
            # 4. 启动浏览器自动化
            result = self._run_browser_automation(valid_urls, wait_per_article, timeout)
            
            # 5. 解析结果
            if result:
                self.extracted_data = self._parse_results()
                
            return self.extracted_data
            
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断操作")
            return None
        except Exception as e:
            print(f"❌ 自动抓取失败: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            # 清理资源
            self._cleanup()
            
            duration = time.time() - start_time
            print(f"\n⏱️ 总耗时: {format_duration(duration)}")
    
    def _validate_urls(self, urls):
        """验证URL列表"""
        valid_urls = []
        
        for i, url in enumerate(urls, 1):
            if validate_wechat_url(url):
                valid_urls.append(url)
                print(f"✅ URL {i}: 有效")
            else:
                print(f"❌ URL {i}: 无效微信文章链接")
        
        if not valid_urls:
            print("❌ 没有有效的微信文章链接")
            return None
            
        print(f"📝 共 {len(valid_urls)} 个有效链接")
        return valid_urls
    
    def _start_proxy(self):
        """启动代理服务"""
        print("\n🔧 启动mitmproxy代理...")

        # 获取拦截器脚本路径
        script_path = os.path.join(os.path.dirname(__file__), 'proxy_interceptor.py')

        if not os.path.exists(script_path):
            print(f"❌ 找不到拦截器脚本: {script_path}")
            return False

        success, port = self.process_manager.start_mitmproxy(script_path)
        if success and port:
            # 更新配置中的端口
            import config
            config.PROXY_PORT = port
            config.PROXY_URL = f"http://{config.PROXY_HOST}:{port}"
            print(f"📡 代理地址已更新: {config.PROXY_URL}")
            return True
        return False
    
    def _run_browser_automation(self, urls, wait_per_article, timeout):
        """运行浏览器自动化"""
        print("\n🌐 启动浏览器自动化...")
        
        try:
            # 使用同步包装器
            browser_sync = WechatBrowserSync()
            
            # 设置超时
            def timeout_handler():
                time.sleep(timeout)
                print(f"\n⏰ 超时 {timeout} 秒，停止抓取")
                return False
            
            # 启动超时线程
            timeout_thread = threading.Thread(target=timeout_handler)
            timeout_thread.daemon = True
            timeout_thread.start()
            
            # 执行浏览器自动化
            results = browser_sync.visit_articles(urls, wait_per_article)
            
            print(f"\n📊 浏览器访问结果:")
            success_count = sum(1 for r in results if r['success'])
            print(f"   成功: {success_count}/{len(results)}")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 浏览器自动化失败: {e}")
            return False
    
    def _parse_results(self):
        """解析抓取结果"""
        print("\n📋 解析抓取结果...")
        
        # 等待文件写入完成
        time.sleep(2)
        
        # 获取最新有效数据
        latest_data = self.data_parser.get_latest_valid_data()
        
        if latest_data:
            print("✅ 成功获取参数:")
            print(f"   时间: {latest_data.get('timestamp', 'N/A')}")
            print(f"   __biz: {latest_data.get('biz', 'N/A')[:30]}...")
            print(f"   appmsg_token: {latest_data.get('appmsg_token', 'N/A')[:30]}...")
            print(f"   Cookie长度: {len(latest_data.get('cookies', ''))}")
            print(f"   Headers数量: {len(latest_data.get('headers', {}))}")
            
            return latest_data
        else:
            print("❌ 未获取到有效参数")
            return None
    
    def _cleanup(self):
        """清理资源"""
        print("\n🧹 清理资源...")
        self.process_manager.cleanup()
    
    def get_extracted_data(self):
        """获取抓取的数据"""
        return self.extracted_data
    
    def save_to_compatible_format(self, output_file="../wechat_keys.txt"):
        """保存为兼容格式，供现有工具使用"""
        if not self.extracted_data:
            print("❌ 没有可保存的数据")
            return False
        
        try:
            # 转换为原有格式
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            with open(output_file, "w", encoding="utf-8") as f:
                f.write("=== 微信公众号Keys和URLs记录 ===\n")
                f.write(f"开始时间: {timestamp}\n\n")
                f.write(f"{'='*60}\n")
                f.write(f"time: {self.extracted_data.get('timestamp', timestamp)}\n")
                f.write(f"allurl: {self.extracted_data.get('url', '')}\n")
                f.write(f"Cookies: {self.extracted_data.get('cookies', '')}\n")
                
                # 保存Headers
                headers = self.extracted_data.get('headers', {})
                if headers:
                    f.write("Headers:\n")
                    for key, value in headers.items():
                        f.write(f"  {key}: {value}\n")
                
                f.write("\n")
            
            print(f"✅ 已保存兼容格式文件: {output_file}")
            return True

        except Exception as e:
            print(f"❌ 保存兼容格式失败: {e}")
            return False


def main():
    """主程序入口"""
    print("🎯 自动化微信公众号参数抓取工具")
    print("=" * 50)

    # 示例使用
    extractor = AutoWechatExtractor()

    # 获取用户输入
    print("\n请输入微信文章链接（每行一个，输入空行结束）:")
    urls = []
    while True:
        url = input().strip()
        if not url:
            break
        urls.append(url)

    if not urls:
        print("❌ 未输入任何链接")
        return

    # 获取配置
    try:
        timeout = int(input(f"超时时间(秒，默认{EXTRACT_CONFIG['max_wait_time']}): ") or EXTRACT_CONFIG['max_wait_time'])
        wait_per_article = int(input("每篇文章等待时间(秒，默认10): ") or "10")
    except ValueError:
        timeout = EXTRACT_CONFIG['max_wait_time']
        wait_per_article = 10
        print("⚠️ 使用默认配置")

    # 执行自动抓取
    result = extractor.auto_extract(urls, timeout, wait_per_article)

    if result:
        print("\n🎉 抓取成功！")

        # 询问是否保存为兼容格式
        save_compatible = input("\n是否保存为兼容格式供现有工具使用？(y/N): ").strip().lower()
        if save_compatible in ['y', 'yes']:
            extractor.save_to_compatible_format()
    else:
        print("\n❌ 抓取失败")
        print("请检查:")
        print("1. 微信文章链接是否正确")
        print("2. 网络连接是否正常")
        print("3. mitmproxy是否正确安装")
        print("4. playwright是否正确安装")


if __name__ == '__main__':
    main()
