#!/usr/bin/env python3
# test_wechat_no_proxy.py - 测试不使用代理访问微信链接

import asyncio
import html
from playwright.async_api import async_playwright

async def test_wechat_without_proxy():
    """测试不使用代理访问微信链接"""
    
    print("🧪 测试Playwright访问微信链接（无代理）")
    print("=" * 50)
    
    # 测试URL
    test_urls = [
        "https://mp.weixin.qq.com",  # 微信公众平台首页
        "http://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&mid=2649923002&idx=1&sn=5f5b5ea491b3afd997354d8eab8deab0&chksm=bf8e2ea346f0989a2800145975b0f4685e5899c1e669ad4cbb1ca1ea9a721c15c3603f7c9483&scene=27#wechat_redirect"
    ]
    
    async with async_playwright() as p:
        try:
            print("🚀 启动浏览器（无代理模式）...")
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器窗口
                slow_mo=1000,    # 慢速模式
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-plugins'
                ]
            )
            
            # 创建上下文（不使用代理）
            context = await browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                ignore_https_errors=True
            )
            
            page = await context.new_page()
            page.set_default_timeout(30000)  # 30秒超时
            
            print("✅ 浏览器启动成功（无代理）")
            
            # 测试每个URL
            for i, url in enumerate(test_urls, 1):
                print(f"\n📖 测试 {i}/{len(test_urls)}: {url}")
                
                try:
                    # 解码URL（如果需要）
                    decoded_url = html.unescape(url)
                    if decoded_url != url:
                        print(f"🔄 URL已解码: {decoded_url}")
                        url = decoded_url
                    
                    # 访问页面
                    print("🌐 正在访问...")
                    response = await page.goto(url, wait_until="networkidle")
                    
                    if response:
                        print(f"📡 响应状态: {response.status}")
                        print(f"📡 最终URL: {response.url}")
                        
                        if response.status == 200:
                            # 获取页面信息
                            title = await page.title()
                            print(f"📄 页面标题: {title}")
                            
                            # 检查页面内容
                            content = await page.content()
                            if "微信" in content or "weixin" in content.lower():
                                print("✅ 确认是微信相关页面")
                            else:
                                print("⚠️ 页面内容可能不是微信页面")
                                
                            # 检查是否需要登录
                            if "登录" in content or "login" in content.lower():
                                print("⚠️ 页面可能需要登录")
                                
                            print("✅ 访问成功")
                            
                        elif response.status == 302 or response.status == 301:
                            print("🔄 页面重定向")
                        else:
                            print(f"⚠️ 非200状态码: {response.status}")
                            
                    else:
                        print("❌ 没有收到响应")
                        
                    # 等待观察
                    print("⏳ 等待3秒...")
                    await asyncio.sleep(3)
                    
                except Exception as e:
                    print(f"❌ 访问失败: {e}")
                    print(f"错误类型: {type(e).__name__}")
            
            await browser.close()
            print("\n✅ 测试完成，浏览器已关闭")
            
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_wechat_without_proxy())
