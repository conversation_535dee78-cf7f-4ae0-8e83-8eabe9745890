# 自动化微信公众号参数抓取工具

## 架构说明

本工具采用 **mitmproxy + Playwright** 的组合方案，实现全自动化的微信公众号参数抓取：

### 核心组件

1. **mitmproxy**：真正的抓包工具，拦截HTTP/HTTPS请求
2. **Playwright**：自动化浏览器，模拟用户访问微信文章
3. **代理配置**：Playwright浏览器通过mitmproxy代理访问网页

### 数据流向

```
浏览器(Playwright) → mitmproxy代理 → 微信服务器
```

### 工作流程

1. 启动mitmproxy代理服务器（端口8080）
2. 启动Playwright浏览器，配置代理为mitmproxy
3. 自动访问指定的微信公众号文章链接
4. mitmproxy拦截并提取cookie、token等关键参数
5. 自动保存参数到文件

## 文件结构

```
auto_wechat_extractor/
├── README.md                    # 说明文档
├── requirements.txt             # 依赖包
├── auto_extractor.py           # 主程序入口
├── proxy_interceptor.py        # mitmproxy拦截器
├── browser_automation.py       # Playwright浏览器自动化
├── config.py                   # 配置文件
└── utils.py                    # 工具函数
```

## 安装依赖

```bash
pip install -r requirements.txt
playwright install chromium
```

## 使用方法

### 快速开始（推荐）

```bash
# 1. 修复依赖（首次使用）
python fix_dependencies.py
# 选择 "5. 全部修复"

# 2. 快速启动
python quick_start.py
# 选择 "1. 快速演示"
```

### 编程使用

```python
from auto_extractor import AutoWechatExtractor

# 创建自动抓取器
extractor = AutoWechatExtractor()

# 自动抓取参数
result = extractor.auto_extract(
    article_urls=[
        "https://mp.weixin.qq.com/s/xxxxx",
        "https://mp.weixin.qq.com/s/yyyyy"
    ],
    timeout=60
)

if result:
    print(f"成功抓取到参数: {result}")
    # 保存为兼容格式
    extractor.save_to_compatible_format("../wechat_keys.txt")
```

## 优势

- ✅ 完全自动化，无需手动操作
- ✅ 支持批量处理多个文章链接
- ✅ 自动处理代理配置和证书
- ✅ 智能重试和错误处理
- ✅ 兼容现有的cookie格式
- ✅ 自动端口检测，避免冲突
- ✅ 依赖问题自动修复

## 问题解决

### 已解决的问题

1. **✅ 端口占用问题**
   - 自动检测可用端口（8081, 8082, 8083...）
   - 智能避开被占用的端口

2. **✅ bcrypt版本兼容性**
   - 自动安装兼容版本
   - 修复mitmproxy启动错误

3. **✅ 依赖安装问题**
   - 提供一键修复工具
   - 自动安装所有必需依赖

### 如果遇到问题

```bash
# 运行修复工具
python fix_dependencies.py
# 选择对应的修复选项
```
