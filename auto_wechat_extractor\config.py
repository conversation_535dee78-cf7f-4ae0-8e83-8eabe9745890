# config.py - 配置文件
"""
自动化微信抓取工具配置
"""

# 代理配置
PROXY_HOST = "127.0.0.1"
PROXY_PORT = 8082  # 改为8081端口避免冲突
PROXY_URL = f"http://{PROXY_HOST}:{PROXY_PORT}"

# 浏览器配置
BROWSER_CONFIG = {
    "headless": False,  # 是否无头模式，调试时建议设为False
    "slow_mo": 1000,    # 操作间隔时间(毫秒)
    "timeout": 30000,   # 页面加载超时时间(毫秒)
    "viewport": {"width": 1280, "height": 720},
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}

# 微信相关配置
WECHAT_DOMAINS = [
    "mp.weixin.qq.com",
    "weixin.qq.com", 
    "wx.qq.com",
    "api.weixin.qq.com"
]

# 关键Cookie名称
KEY_COOKIES = [
    "session_key", "uin", "skey", "p_skey", 
    "wxuin", "data_bizuin", "appmsg_token", 
    "pass_ticket", "wap_sid2"
]

# 关键Headers
KEY_HEADERS = [
    'x-wechat-key', 'x-wechat-uin', 'exportkey',
    'user-agent', 'accept', 'accept-language',
    'cache-control', 'sec-fetch-site', 'sec-fetch-mode',
    'sec-fetch-dest', 'priority'
]

# 输出文件配置
OUTPUT_FILE = "wechat_keys_auto.txt"
BACKUP_DIR = "backup"

# 抓取配置
EXTRACT_CONFIG = {
    "max_wait_time": 60,      # 最大等待时间(秒)
    "retry_times": 3,         # 重试次数
    "page_load_wait": 5,      # 页面加载等待时间(秒)
    "scroll_wait": 2,         # 滚动等待时间(秒)
    "min_cookie_length": 50,  # 最小cookie长度
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "auto_extractor.log"
}
