#!/usr/bin/env python3
# test_network.py - 测试网络连接问题

import asyncio
from playwright.async_api import async_playwright

async def test_network_connection():
    """测试Playwright的网络连接"""
    
    print("🌐 Playwright网络连接测试")
    print("=" * 40)
    
    async with async_playwright() as p:
        try:
            print("🚀 启动浏览器...")
            
            # 尝试不同的启动参数
            browser = await p.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--ignore-certificate-errors-spki-list',
                    '--disable-extensions',
                    '--no-first-run',
                    '--disable-default-apps'
                ]
            )
            
            print("✅ 浏览器启动成功")
            
            # 创建上下文
            context = await browser.new_context(
                ignore_https_errors=True,
                bypass_csp=True,
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            page = await context.new_page()
            page.set_default_timeout(30000)
            
            print("✅ 页面创建成功")
            
            # 测试简单的网站
            test_sites = [
                "http://httpbin.org/get",  # 简单的HTTP测试
                "https://www.baidu.com",   # 百度
                "http://www.baidu.com",    # HTTP版本的百度
            ]
            
            for site in test_sites:
                print(f"\n🌐 测试访问: {site}")
                try:
                    response = await page.goto(site, timeout=15000)
                    if response:
                        print(f"✅ 成功访问，状态码: {response.status}")
                        title = await page.title()
                        print(f"📄 页面标题: {title}")
                    else:
                        print("❌ 没有响应")
                        
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    print(f"❌ 访问失败: {e}")
                    print(f"错误类型: {type(e).__name__}")
            
            # 测试微信相关域名
            print(f"\n🌐 测试微信域名...")
            try:
                response = await page.goto("https://mp.weixin.qq.com", timeout=15000)
                if response:
                    print(f"✅ 微信域名访问成功，状态码: {response.status}")
                    title = await page.title()
                    print(f"📄 页面标题: {title}")
                else:
                    print("❌ 微信域名无响应")
            except Exception as e:
                print(f"❌ 微信域名访问失败: {e}")
            
            await asyncio.sleep(3)
            await browser.close()
            print("\n✅ 测试完成")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

async def test_system_proxy():
    """检查系统代理设置"""
    print("\n🔍 检查系统代理设置")
    print("=" * 30)
    
    import os
    
    # 检查环境变量中的代理设置
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"⚠️ 发现代理设置 {var}: {value}")
        else:
            print(f"✅ {var}: 未设置")

if __name__ == "__main__":
    asyncio.run(test_system_proxy())
    asyncio.run(test_network_connection())
